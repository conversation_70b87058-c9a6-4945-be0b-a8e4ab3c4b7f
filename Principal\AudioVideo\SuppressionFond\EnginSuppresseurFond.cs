﻿using Ceras;
using ComputeSharp;
using ComputeSharp.D2D1.Interop;
using DirectX;
using Microsoft.ML.OnnxRuntime;
using Microsoft.VisualStudio.Threading;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using Utils;
using Vortice.Direct2D1;
using Vortice.Direct3D11;
using Vortice.Direct3D12;
using Vortice.DirectML;
using Vortice.DXGI;
using Vortice.Mathematics;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.TrayNotify;
using static AudioVidéo.SuppressionFond.OnnxRuntimeAddOns;
using InterpolationMode = Vortice.Direct2D1.InterpolationMode;
using Rect = Vortice.Mathematics.Rect;
using ComputeSharp.Interop;
using System.Threading;
using SharpGen.Runtime;
using System.Text;
using Microsoft.ML.OnnxRuntime.Tensors;
using Principal;
using AudioVidéo;
using EditeurAudioVideoCpp;
using MediaFoundation;
using System.Runtime.InteropServices;
using MfEav;
using Vortice.WIC;
using AlphaCodec;
using TurboJpegWrapper;
using DocumentFormat.OpenXml.Office2010.Excel;
using Microsoft.ConcurrencyVisualizer.Instrumentation;
using Vanara.Extensions;
using ComputeSharp.Resources;

namespace AudioVidéo.SuppressionFond;

///
/// IMPORTANT: 1) ONNX resource states are always D3D12_RESOURCE_STATE_UNORDERED_ACCESS after infering!
///               See ExecutionProvider.cpp in the DmlExecutionProvider folder.
///            2) Vortice version of DirectML must be same as the one used by Microsoft.ML.OnnxRuntime.DirectML

public partial class EnginSuppresseurFond : IDisposable
{
    #region variables
    bool _continuousFrameFlow;
    protected ReadWriteTexture2D<Bgra32, float4> _readWriteInputTexture;
    protected ReadWriteBuffer<float> _readWriteInputBuffer;

    ID3D12Resource1 _sharedD3D12ReadWriteTexture;
    nint _handleSharedResourceReadWriteTexture = -1; // -1 = unitialized.
    ID3D11Resource _mFd3D11SharedResourceReadWriteTexture;
    ulong _fenceValue = 1;

    ID3D12CommandQueue _d3D12CommandQueue;
    ID3D12CommandAllocator _d3D12CommandAllocator;
    ID3D12GraphicsCommandList4 _d3D12CommandList;
    ID3D12Fence _d3D12CopyFence;

    string _modèle;
    public string Modèle => _modèle;
    protected InferenceSession _inferenceSession;
    protected ReadWriteBuffer<float> _readWriteAlphaBuffer;
    ReadWriteTexture2D<R8, float> _readWriteAlphaTexture;
    ReadWriteTexture2D<R8, float> _readWriteAlphaTexturePreviousPrevious;
    ReadWriteTexture2D<R8, float> _readWriteAlphaTexturePrevious;
    ReadWriteTexture2D<Bgra32, float4> _readWriteDebugTexture;
    ReadWriteTexture2D<Bgra32, float4> _readWriteBackgroundGroupsTexture;
    ReadWriteTexture2D<Bgra32, float4> _readWriteBackgroundGroupsHTexture;
    ReadWriteTexture2D<Bgra32, float4> _readWriteBackgroundGroupsVTexture;
    ReadWriteTexture2D<Bgra32, float4> _readWriteBackgroundGroupsFinalTexture;
    ReadBackTexture2D<Bgra32> _readBackBackgroundGroupsFinalTexture;

    ReadWriteBuffer<GroupInfo> _readWriteBufferGroupInfos;

    List<nint> _dmlResourcesToFree = new();
    protected OrtValue _source;
    protected OrtValue _alpha;

    protected DirectXParameters _parameters;

    protected int _largeurImages, _hauteurImages;

    public const int GroupSize = 16; // Must be a divisor of the imageVidéo size).

    LecteurFichierMédia _lecteurFichierMédia;
    private bool _disposedValue;

    public delegate void RunInference();
    RunInference _runInferenceCallback;

    int _previousPreviousImageId = int.MinValue;
    int _previousImageId = int.MinValue;
    byte[] _previousBytesBackgroundGroups = null;

    TJCompressor _tJCompressor;


    #endregion
    protected void Initiliaser(DirectXParameters parameters, FichierMédia fichierMédia, int largeurImages, int hauteurImages, string modèle, RunInference runInferenceCallback, bool continuousFrameFlow)
    {
        _parameters = parameters;
        _modèle = modèle;
        _runInferenceCallback = runInferenceCallback;
        _continuousFrameFlow = continuousFrameFlow;

        CommandQueueDescription commandQueueDesc = new()
        {
            Type = CommandListType.Direct,
            Flags = CommandQueueFlags.None,
        };
        _d3D12CommandQueue = _parameters.D3D12Device.CreateCommandQueue(commandQueueDesc);
        _d3D12CommandAllocator = _parameters.D3D12Device.CreateCommandAllocator(CommandListType.Direct);
        _d3D12CommandList = _parameters.D3D12Device.CreateCommandList<ID3D12GraphicsCommandList4>(CommandListType.Direct, _d3D12CommandAllocator);
        _d3D12CopyFence = _parameters.D3D12Device.CreateFence(0, Vortice.Direct3D12.FenceFlags.None);

        _readWriteInputTexture = _parameters.GraphicsDevice.AllocateReadWriteTexture2D<Bgra32, float4>(_largeurImages, _hauteurImages);
        _readWriteInputBuffer = _parameters.GraphicsDevice.AllocateReadWriteBuffer<float>(_largeurImages * _hauteurImages * 3);
        _readWriteAlphaBuffer = _parameters.GraphicsDevice.AllocateReadWriteBuffer<float>(_largeurImages * _hauteurImages);
        _readWriteAlphaTexture = _parameters.GraphicsDevice.AllocateReadWriteTexture2D<R8, float>(_largeurImages, _hauteurImages);
        if (continuousFrameFlow)
        {
            _readWriteAlphaTexturePreviousPrevious = _parameters.GraphicsDevice.AllocateReadWriteTexture2D<R8, float>(_largeurImages, _hauteurImages);
            _readWriteAlphaTexturePrevious = _parameters.GraphicsDevice.AllocateReadWriteTexture2D<R8, float>(_largeurImages, _hauteurImages);
        }
        _readWriteDebugTexture = _parameters.GraphicsDevice.AllocateReadWriteTexture2D<Bgra32, float4>(_largeurImages, _hauteurImages);
        _readWriteBackgroundGroupsTexture = _parameters.GraphicsDevice.AllocateReadWriteTexture2D<Bgra32, float4>(_largeurImages / GroupSize, _hauteurImages / GroupSize);
        _readWriteBackgroundGroupsHTexture = _parameters.GraphicsDevice.AllocateReadWriteTexture2D<Bgra32, float4>(_largeurImages / GroupSize, _hauteurImages / GroupSize);
        _readWriteBackgroundGroupsVTexture = _parameters.GraphicsDevice.AllocateReadWriteTexture2D<Bgra32, float4>(_largeurImages / GroupSize, _hauteurImages / GroupSize);
        _readWriteBackgroundGroupsFinalTexture = _parameters.GraphicsDevice.AllocateReadWriteTexture2D<Bgra32, float4>(_largeurImages / GroupSize, _hauteurImages / GroupSize);
        _readBackBackgroundGroupsFinalTexture = _parameters.GraphicsDevice.AllocateReadBackTexture2D<Bgra32>(_largeurImages / GroupSize, _hauteurImages / GroupSize);
        _readWriteBufferGroupInfos = _parameters.GraphicsDevice.AllocateReadWriteBuffer<GroupInfo>(_largeurImages / GroupSize * _hauteurImages / GroupSize);

        _source = CreateOrtValue(_readWriteInputBuffer, _largeurImages * _hauteurImages * sizeof(float) * 3, 3);
        _alpha = CreateOrtValue(_readWriteAlphaBuffer, _largeurImages * _hauteurImages * sizeof(float), 1);

        HeapProperties headprops = new(HeapType.Default);
        ResourceDescription resDesc = ResourceDescription.Texture2D(Vortice.DXGI.Format.B8G8R8A8_UNorm, (uint)_largeurImages, (uint)_hauteurImages, 1, 1, 1, 0, ResourceFlags.AllowRenderTarget | ResourceFlags.AllowSimultaneousAccess);
        _parameters.D3D12Device.CreateCommittedResource(headprops, HeapFlags.Shared, resDesc, ResourceStates.Common, null, out _sharedD3D12ReadWriteTexture);
        _handleSharedResourceReadWriteTexture = _parameters.D3D12Device.CreateSharedHandle(_sharedD3D12ReadWriteTexture, null, null);
        _mFd3D11SharedResourceReadWriteTexture = MFInitialization.DirectXParameters.D3D11Device.OpenSharedResource1<ID3D11Resource>(_handleSharedResourceReadWriteTexture);

        string exePath = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location);
        string model_path = exePath + $@"\AudioVideo\SuppressionFond\Models\{modèle}";
        if (!File.Exists(model_path))
        {
            Debugger.Break();
            return;
        }

        using SessionOptionsEx sessionOptions = new SessionOptionsEx()
        {
            GraphOptimizationLevel = GraphOptimizationLevel.ORT_ENABLE_ALL,
            ExecutionMode = ExecutionMode.ORT_SEQUENTIAL, // Required for DirectMl.
            EnableMemoryPattern = false, // Required for DirectMl.
            EnableCpuMemArena = true, 
        };
        sessionOptions.AppendExecutionProvider_DML1(_parameters.DmlDevice, _d3D12CommandQueue);
        _inferenceSession = new InferenceSession(model_path, sessionOptions);

        _lecteurFichierMédia = new(fichierMédia, _largeurImages, _hauteurImages, MFMediaType.ARGB32);

        _tJCompressor = new TJCompressor();
    }

    ID3D12Resource1 GetResourceOf<T>(ComputeSharp.Resources.Buffer<T> buffer) where T : unmanaged
    {
        unsafe
        {
            var guid = typeof(ID3D12Resource).GUID;
            void* ptr;
            InteropServices.GetID3D12Resource<T>(buffer, &guid, &ptr);
            ID3D12Resource1 resource = new(new nint(ptr));
            return resource;
        }
    }

    ID3D12Resource1 GetResourceOf<T>(ComputeSharp.Resources.Texture2D<T> buffer) where T : unmanaged
    {
        unsafe
        {
            var guid = typeof(ID3D12Resource).GUID;
            void* ptr;
            InteropServices.GetID3D12Resource<T>(buffer, &guid, &ptr);
            ID3D12Resource1 resource = new(new nint(ptr));
            return resource;
        }
    }

    #region Shaders

    [ThreadGroupSize(DefaultThreadGroupSizes.XY)]
    [GeneratedComputeShaderDescriptor]
    public readonly partial struct CopyAlpha(ReadWriteBuffer<float> bufferIn, IReadWriteNormalizedTexture2D<float> bufferOut) : IComputeShader
    {
        public void Execute()
        {
            int index = ThreadIds.Y * DispatchSize.X + ThreadIds.X;
            bufferOut[ThreadIds.XY] = bufferIn[index];
        }
    }

    [ThreadGroupSize(DefaultThreadGroupSizes.XY)]
    [GeneratedComputeShaderDescriptor]
    public readonly partial struct FindBackgroundGroups(IReadWriteNormalizedTexture2D<float4> inputTexture, ReadWriteBuffer<float> readWriteInputBufferAlpha, IReadWriteNormalizedTexture2D<float4> backgroundGroupsTexture, ReadWriteBuffer<GroupInfo> groupInfos) : IComputeShader
    {
        public void Execute()
        {
            bool fullyTransparent = true;
            bool fullyOpaque = true;
            float3 background = default;
            int widthGroups = DispatchSize.X;
            int widthPixels = widthGroups * GroupSize;
            int xGroup = ThreadIds.X;
            int yGroup = ThreadIds.Y;
            int indexGroup = yGroup * widthGroups + xGroup;
            int flags = 0;
            for (int deltaXGroup = 0; deltaXGroup < GroupSize; deltaXGroup++)
            {
                for (int deltaYGroup = 0; deltaYGroup < GroupSize; deltaYGroup++)
                {
                    int x = xGroup * GroupSize + deltaXGroup;
                    int y = yGroup * GroupSize + deltaYGroup;
                    float alpha = readWriteInputBufferAlpha[y * widthPixels + x];
                    if (alpha < 1f / 255)
                    {
                        flags |= GroupInfo.ContainsFullyTransparentPixels;
                        fullyOpaque = false;
                    }
                    else if (alpha > 254f / 255)
                    {
                        flags |= GroupInfo.ContainsFullyOpaquePixels;
                        fullyTransparent = false;
                    }
                    else
                    {
                        flags |= GroupInfo.ContainsPartiallyOpaquePixels;
                        fullyTransparent = false;
                        fullyOpaque = false;
                    }
                    background += inputTexture[x, y].XYZ;
                }
            }
            backgroundGroupsTexture[ThreadIds.X, ThreadIds.Y] = new(background / (GroupSize * GroupSize), 1);
            if (fullyTransparent) flags |= GroupInfo.FullyTransparent;
            if (fullyOpaque) flags |= GroupInfo.FullyOpaque;
            groupInfos[indexGroup].Flags = flags;
            groupInfos[indexGroup].ReliabilityH = 0;
            groupInfos[indexGroup].ReliabilityV = 0;
        }
    }

    [ThreadGroupSize(DefaultThreadGroupSizes.X)]
    [GeneratedComputeShaderDescriptor]
    public readonly partial struct FindBackgroundGroupsH(IReadWriteNormalizedTexture2D<float4> backgroundGroupsTexture, int heightInGroups, IReadWriteNormalizedTexture2D<float4> backgroundGroupsHTexture, ReadWriteBuffer<GroupInfo> groupInfos) : IComputeShader
    {
        public void Execute()
        {
            int xGroup = ThreadIds.X;
            int widthGroups = DispatchSize.X;

            // On trouve les points inconnus.
            for (int yGroup = 0; yGroup < heightInGroups; yGroup++)
            {
                int indexGroup = yGroup * widthGroups + xGroup;
                if ((groupInfos[indexGroup].Flags & GroupInfo.FullyTransparent) != 0)
                    backgroundGroupsHTexture[xGroup, yGroup] = backgroundGroupsTexture[xGroup, yGroup];
                else
                {
                    int yGroupStart = yGroup - 1;
                    float4 colorStart = backgroundGroupsTexture[xGroup, yGroupStart];
                    for (; yGroup < heightInGroups; yGroup++)
                    {
                        indexGroup = yGroup * widthGroups + xGroup;
                        if ((groupInfos[indexGroup].Flags & GroupInfo.FullyTransparent) != 0) break;
                    }
                    int yGroupEnd = yGroup--;  // Post-decrement because loop will increase it by one but the value still must be  processed.
                    float4 colorEnd;
                    if (yGroupEnd == heightInGroups) colorEnd = colorStart;
                    else colorEnd = backgroundGroupsTexture[xGroup, yGroupEnd];
                    int groupDelta = yGroupEnd - yGroupStart;
                    for (int yGroupT = yGroupStart + 1; yGroupT < yGroupEnd; yGroupT++)
                    {
                        int distance;
                        if (yGroupEnd == heightInGroups)
                            distance = yGroupT - yGroupStart;
                        else distance = Hlsl.Min(yGroupT - yGroupStart, yGroupEnd - yGroupT);
                        float reliability = 1f - (float)distance / heightInGroups; // NB: A distance of heightInGroups produces a reliability of 0!
                        float ratio = (yGroupT - yGroupStart) / (float)groupDelta;
                        backgroundGroupsHTexture[xGroup, yGroupT] = Hlsl.Lerp(colorStart, colorEnd, ratio);
                        groupInfos[yGroupT * DispatchSize.X + xGroup].ReliabilityH = reliability;
                    }
                }
            }
        }
    }

    [ThreadGroupSize(DefaultThreadGroupSizes.Y)]
    [GeneratedComputeShaderDescriptor]
    public readonly partial struct FindBackgroundGroupsV(IReadWriteNormalizedTexture2D<float4> backgroundGroupsTexture, int widthGroups, IReadWriteNormalizedTexture2D<float4> backgroundGroupsVTexture, ReadWriteBuffer<GroupInfo> groupInfos) : IComputeShader
    {
        public void Execute()
        {
            int yGroup = ThreadIds.Y;

            for (int xGroup = 0; xGroup < widthGroups; xGroup++)
            {
                int indexGroup = yGroup * widthGroups + xGroup;
                if ((groupInfos[indexGroup].Flags & GroupInfo.FullyTransparent) != 0)
                    backgroundGroupsVTexture[xGroup, yGroup] = backgroundGroupsTexture[xGroup, yGroup];
                else
                {
                    int xGroupStart = xGroup - 1;
                    float4 colorStart = backgroundGroupsTexture[xGroupStart, yGroup];
                    for (; xGroup < widthGroups; xGroup++)
                    {
                        indexGroup = yGroup * widthGroups + xGroup;
                        if ((groupInfos[indexGroup].Flags & GroupInfo.FullyTransparent) != 0) break;
                    }
                    int xGroupEnd = xGroup--; // Post decrement because loop will increase it by one but the value still must be  processed.
                    float4 colorEnd;
                    if (xGroupEnd == widthGroups) colorEnd = colorStart;
                    else colorEnd = backgroundGroupsTexture[xGroupEnd, yGroup];
                    int groupDelta = xGroupEnd - xGroupStart;
                    for (int xGroupT = xGroupStart + 1; xGroupT < xGroupEnd; xGroupT++)
                    {
                        int distance;
                        if (xGroupEnd == widthGroups)
                            distance = xGroupT - xGroupStart;
                        else distance = Hlsl.Min(xGroupT - xGroupStart, xGroupEnd - xGroupT);
                        float reliability = 1f - (float)distance / widthGroups; // NB: A distance of width produces a reliability of 0!
                        float ratio = (xGroupT - xGroupStart) / (float)groupDelta;
                        backgroundGroupsVTexture[xGroupT, yGroup] = Hlsl.Lerp(colorStart, colorEnd, ratio);
                        groupInfos[yGroup * widthGroups + xGroupT].ReliabilityV = reliability;
                    }
                }
            }
        }
    }

    [ThreadGroupSize(DefaultThreadGroupSizes.XY)]
    [GeneratedComputeShaderDescriptor]
    public readonly partial struct FindBackgroundGroupsFinal(IReadWriteNormalizedTexture2D<float4> backgroundGroupsHTexture, IReadWriteNormalizedTexture2D<float4> backgroundGroupsVTexture, ReadWriteBuffer<GroupInfo> groupInfos, IReadWriteNormalizedTexture2D<float4> backgroundGroupsFinalTexture) : IComputeShader
    {
        public void Execute()
        {
            int xGroup = ThreadIds.X;
            int yGroup = ThreadIds.Y;
            int widthGroups = DispatchSize.X;
            int indexGroup = yGroup * widthGroups + xGroup;

            var reliabilityH = groupInfos[indexGroup].ReliabilityH;
            var reliabilityV = groupInfos[indexGroup].ReliabilityV;
            backgroundGroupsFinalTexture[xGroup, yGroup] = (backgroundGroupsHTexture[xGroup, yGroup] * reliabilityH + backgroundGroupsVTexture[xGroup, yGroup] * reliabilityV) / (reliabilityH + reliabilityV);
        }
    }

    #endregion

    void WaitCommandQueueFinished()
    {
        // Signal the fence at the end of the command list.
        _d3D12CommandQueue.Signal(_d3D12CopyFence, _fenceValue);

        // Wait for the fence on the Cpu side.
        if (_d3D12CopyFence.CompletedValue < _fenceValue)
        {
            using (var fenceEvent = new AutoResetEvent(false))
            {
                if (_d3D12CopyFence.SetEventOnCompletion(_fenceValue, fenceEvent) != Result.Ok) Debugger.Break();
                fenceEvent.WaitOne();
            }
        }
        _fenceValue++;
    }
    void CopyResource(ID3D12Resource source, ResourceStates sourceState, ID3D12Resource destination, ResourceStates destinationState)
    {
        var barrier1 = ResourceBarrier.BarrierTransition(source, sourceState, ResourceStates.CopySource);
        _d3D12CommandList.ResourceBarrier(barrier1);
        var barrier2 = ResourceBarrier.BarrierTransition(destination, destinationState, ResourceStates.CopyDest);
        _d3D12CommandList.ResourceBarrier(barrier2);

        Box box;
        if (source.Description.Width > destination.Description.Width)
            box = new(0, 0, 0, (int)destination.Description.Width, (int)destination.Description.Height, 1);
        else box = new(0, 0, 0, (int)source.Description.Width, (int)source.Description.Height, 1);
        _d3D12CommandList.CopyTextureRegion(new TextureCopyLocation(destination), 0, 0, 0, new TextureCopyLocation(source), box);

        //_d3D12CommandList.CopyResource(destination, source);

        barrier1 = ResourceBarrier.BarrierTransition(source, ResourceStates.CopySource, sourceState);
        _d3D12CommandList.ResourceBarrier(barrier1);
        barrier2 = ResourceBarrier.BarrierTransition(destination, ResourceStates.CopyDest, destinationState);
        _d3D12CommandList.ResourceBarrier(barrier2);
        _d3D12CommandList.Close();
        _d3D12CommandQueue.ExecuteCommandList(_d3D12CommandList);

        WaitCommandQueueFinished();

        _d3D12CommandAllocator.Reset();
        _d3D12CommandList.Reset(_d3D12CommandAllocator);
    }

    protected OrtValue CreateOrtValue<T>(ReadWriteBuffer<T> buffer, int dataSize, int numberOfChannels) where T : unmanaged
    {
        // Create the dml bufferesource from the D3D bufferesource.
        using var bufferResource = GetResourceOf(buffer);
        CreateGPUAllocationFromD3DResource(bufferResource, out var dmlResource);
        _dmlResourcesToFree.Add(dmlResource);
        int deviceId = 0; // Not used for DirectMl.
        using OrtMemoryInfo mem_info = new(Encoding.UTF8.GetBytes("DML" + Char.MinValue), OrtAllocatorType.DeviceAllocator, deviceId, OrtMemType.Default);

        long[] shape = { 1, numberOfChannels, _hauteurImages, _largeurImages };
        var ortValue = OrtValue.CreateTensorValueWithData(mem_info, TensorElementType.Float, shape, dmlResource, dataSize);

        return ortValue;
    }
#pragma warning disable IDE0051 // Supprimer les membres privés non utilisés
    OrtValue CreateOrtValue<T, TPixel>(ReadWriteTexture2D<T, TPixel> texture, int dataSize, int numberOfChannels) where T : unmanaged, IPixel<T, TPixel> where TPixel : unmanaged
#pragma warning restore IDE0051 // Supprimer les membres privés non utilisés
    {
        // Create the dml bufferesource from the D3D bufferesource.
        using var bufferResource = GetResourceOf(texture);
        CreateGPUAllocationFromD3DResource(bufferResource, out var dmlResource);
        _dmlResourcesToFree.Add(dmlResource);
        int deviceId = 0; // Not used for DirectMl.
        using OrtMemoryInfo mem_info = new(Encoding.UTF8.GetBytes("DML" + Char.MinValue), OrtAllocatorType.DeviceAllocator, deviceId, OrtMemType.Default);

        long[] shape = { 1, numberOfChannels, _hauteurImages, _largeurImages };
        var ortValue = OrtValue.CreateTensorValueWithData(mem_info, TensorElementType.Float, shape, dmlResource, dataSize);

        return ortValue;
    }

    public void UpdateInputTextureFromImfSample(IMFSample sample, ComputeSharp.Resources.Texture2D<Bgra32> bufferTo)
    {
        sample.GetBufferCount(out int bufferCount).CheckHRResult();
        IMFMediaBuffer mediaBuffer;
        if (bufferCount == 1)
            sample.GetBufferByIndex(0, out mediaBuffer).CheckHRResult();
        else sample.ConvertToContiguousBuffer(out mediaBuffer).CheckHRResult();
        try
        {
            Guid guid = typeof(IMFDXGIBuffer).GUID;
            IntPtr pUnknownMediaBuffer = Marshal.GetIUnknownForObject(mediaBuffer);
            var hr = Marshal.QueryInterface(pUnknownMediaBuffer, ref guid, out IntPtr pUnknownBuffer);
            if (hr == 0 && pUnknownBuffer != IntPtr.Zero)
            {
                IMFDXGIBuffer mFDxgiBuffer = (IMFDXGIBuffer)Marshal.GetTypedObjectForIUnknown(pUnknownBuffer, typeof(IMFDXGIBuffer));
                object surfaceObject;
                hr = (int)mFDxgiBuffer.GetResource(typeof(IDXGISurface2).GUID, out surfaceObject);
                if (hr < 0) Marshal.ThrowExceptionForHR(hr);
                var pDXGISurface = Marshal.GetIUnknownForObject(surfaceObject);
                Marshal.ReleaseComObject(surfaceObject);
                using IDXGISurface2 sampleSurface = new(pDXGISurface);
                //Marshal.ReleaseReference(pDXGISurface); // Must not release this one because it releases the imageTexture's surface!
                mFDxgiBuffer.GetSubresourceIndex(out int subResourceIndex);

                Marshal.Release(pUnknownMediaBuffer);
                Marshal.ReleaseComObject(mFDxgiBuffer);
                Marshal.Release(pUnknownBuffer);
                using var inputTexture2D = sampleSurface.QueryInterface<ID3D11Texture2D>();
                QueryDescription queryDescription = new QueryDescription()
                {
                    MiscFlags = QueryFlags.None,
                    QueryType = Vortice.Direct3D11.QueryType.Event
                };
                DirectXParameters mfParameters = MFInitialization.DirectXParameters;
                using var query = mfParameters.D3D11Device.CreateQuery(queryDescription);
                mfParameters.D3D11Multithread.Enter(); // NB: Le immediate context n'est pas thread-safe!
                // Parameters.D3D11DeviceContext.Begin(query); NB: Begin not supported for Event Query!
                Box box = new(0, 0, 0, (int)inputTexture2D.Description.Width, (int)inputTexture2D.Description.Height, 1);
                mfParameters.D3D11DeviceContext.CopySubresourceRegion(_mFd3D11SharedResourceReadWriteTexture, 0, 0, 0, 0, inputTexture2D, 0, box);
                //Parameters.D3D11DeviceContext.CopyResource(_mFd3D11SharedResourceReadWriteTexture, d3D11Texture2DFrom);
                mfParameters.D3D11DeviceContext.Flush();
                mfParameters.D3D11DeviceContext.End(query);
                SpinWait.SpinUntil(() => mfParameters.D3D11DeviceContext.IsDataAvailable(query), 1000);
                mfParameters.D3D11Multithread.Leave(); // NB: Le immediate context n'est pas thread-safe!
                using var bufferResource = GetResourceOf(bufferTo);
                CopyResource(_sharedD3D12ReadWriteTexture, ResourceStates.Common, bufferResource, ResourceStates.UnorderedAccess);
            }
            else Marshal.Release(pUnknownMediaBuffer);
        }
        catch (Exception ex)
        {
            General.WriteDebug(ex.Message);
            Debugger.Break();
        }
        finally
        {
            mediaBuffer.Unlock();
            Marshal.ReleaseComObject(mediaBuffer);
        }
    }

    [ThreadGroupSize(DefaultThreadGroupSizes.XY)]
    [GeneratedComputeShaderDescriptor]
    public readonly partial struct AppliquerAntiscintillement(IReadWriteNormalizedTexture2D<float> alphaPrevious, IReadWriteNormalizedTexture2D<float> alpha, IReadWriteNormalizedTexture2D<float> alphaAfter) : IComputeShader
    {
        public void Execute()
        {
            int index = ThreadIds.Y * DispatchSize.X + ThreadIds.X;
            if ((alphaPrevious[ThreadIds.XY] > alpha[ThreadIds.XY] && alphaAfter[ThreadIds.XY] > alpha[ThreadIds.XY]) ||
                (alphaPrevious[ThreadIds.XY] < alpha[ThreadIds.XY] && alphaAfter[ThreadIds.XY] < alpha[ThreadIds.XY]))
                alpha[ThreadIds.XY] = (alphaPrevious[ThreadIds.XY] + alphaAfter[ThreadIds.XY]) / 2;
        }
    }

    public void Process(ImageVidéo imageVidéo, FiltreSuppressionFond filtreSuppressionFond, ImageVidéo imageVidéoPrécédente = null)
    {
        try
        {
            Stopwatch stopwatch = Stopwatch.StartNew();

            var imageTexture = _lecteurFichierMédia.LireSample(imageVidéo);

#if false // To save file to disk...
            sample.SaveToDiskPng(MFInitialization.DirectXParameters, @"f:\test0.png");
#endif

            _readWriteInputTexture.CopyFrom().CopyFrom(imageTexture.RawTexture, 0, 0, 0, 0);
            //_readWriteInputTexture.Save(@"F:\test5.png");
#if false
            UpdateInputTextureFromImfSample(sample, _readWriteDebugTexture);
#endif
            _runInferenceCallback();

            WaitCommandQueueFinished();

            _parameters.GraphicsDevice.For(_largeurImages, _hauteurImages, new CopyAlpha(_readWriteAlphaBuffer, _readWriteAlphaTexture));

            _parameters.GraphicsDevice.For(_largeurImages / GroupSize, _hauteurImages / GroupSize, new FindBackgroundGroups(_readWriteInputTexture, _readWriteAlphaBuffer, _readWriteBackgroundGroupsTexture, _readWriteBufferGroupInfos));

            _parameters.GraphicsDevice.For(_largeurImages / GroupSize, new FindBackgroundGroupsH(_readWriteBackgroundGroupsTexture, _hauteurImages / GroupSize, _readWriteBackgroundGroupsHTexture, _readWriteBufferGroupInfos));

            _parameters.GraphicsDevice.For(1, _hauteurImages / GroupSize, new FindBackgroundGroupsV(_readWriteBackgroundGroupsTexture, _largeurImages / GroupSize, _readWriteBackgroundGroupsVTexture, _readWriteBufferGroupInfos));

            _parameters.GraphicsDevice.For(_largeurImages / GroupSize, _hauteurImages / GroupSize, new FindBackgroundGroupsFinal(_readWriteBackgroundGroupsHTexture, _readWriteBackgroundGroupsVTexture, _readWriteBufferGroupInfos, _readWriteBackgroundGroupsFinalTexture));

            byte[] bytesBackgroundGroups = null;

            _readWriteBackgroundGroupsFinalTexture.CopyTo(_readBackBackgroundGroupsFinalTexture);
            TextureView2D<Bgra32> textureView = _readBackBackgroundGroupsFinalTexture.View;
            unsafe
            {
                Bgra32* data = textureView.DangerousGetAddressAndByteStride(out int strideInBytes);
                bytesBackgroundGroups = _tJCompressor.Compress(new nint(data), strideInBytes, _largeurImages / GroupSize, _hauteurImages / GroupSize, TJPixelFormat.BGRA, TJSubsamplingOption.Chrominance420, quality: 70);
            }

            //_readWriteBackgroundGroupsFinalTexture.Save(@"F:\test.png");
            stopwatch.Stop();
            //measurements.Add($"p3: {stopwatch.Elapsed}");


            //using TJDecompressor decompressor = new TJDecompressor();
            //for (int yy = 0; yy < 4; yy++)
            //{
            //    stopwatch.Restart();
            //    decompressor.Decompress(bytesCompressedBackgroundGroups, TJPixelFormat.RGB, TJFlags.None, out int width, out int height, out int stride);
            //    stopwatch.StopCommunication();
            //}

            //_readWriteAlphaTexture.Save(@"f:\test.png");

            bool processDefault = true;
            if (_continuousFrameFlow)
            {
                if (_previousPreviousImageId != int.MinValue)
                {
                    // On enlève le scintillement de l'alpha précédent et on le sauve.
                    _parameters.GraphicsDevice.For(_largeurImages, _hauteurImages, new AppliquerAntiscintillement(_readWriteAlphaTexturePreviousPrevious, _readWriteAlphaTexturePrevious, _readWriteAlphaTexture));
                    var alphaData = AlphaData.Create(_parameters, _readWriteAlphaTexturePrevious);
                    DonnéesAlpha donnéesAlpha = new(_previousImageId, alphaData, GroupSize, _previousBytesBackgroundGroups, DateTime.Now); // NB: +1 because in litedb, ids of 0 mean uninitialized!
                    Stopwatch stopwatch1 = Stopwatch.StartNew();
                    LeProjet.DonnéesAlphaStore.Upsert(donnéesAlpha);
                    stopwatch1.Stop();
                    // Prepare for next frame.
                    (_readWriteAlphaTexturePreviousPrevious, _readWriteAlphaTexturePrevious) = (_readWriteAlphaTexturePrevious, _readWriteAlphaTexturePreviousPrevious);
                    (_readWriteAlphaTexturePrevious, _readWriteAlphaTexture) = (_readWriteAlphaTexture, _readWriteAlphaTexturePrevious);
                    processDefault = false;
                }
                _previousPreviousImageId = _previousImageId;
                _previousImageId = imageVidéo.Id;
                _previousBytesBackgroundGroups = bytesBackgroundGroups;
            }
            if (processDefault)
            {
                var alphaData = AlphaData.Create(_parameters, _readWriteAlphaTexture);
                // We add one to the id because in litedb, ids of 0 mean uninitialized!
                DonnéesAlpha donnéesAlpha = new(imageVidéo.Id, alphaData, GroupSize, bytesBackgroundGroups, DateTime.Now);
                LeProjet.DonnéesAlphaStore.Upsert(donnéesAlpha);
                _previousPreviousImageId = _previousImageId = int.MinValue;
            }
            //_readWriteOutputTexture.Save(@"F:\test.png");

        }
        catch (Exception ex)
        {
            General.WriteDebug(ex.Message);
            Debugger.Break();
        }
    }
    public void TerminateContinuousFlow()
    {
        if (!_continuousFrameFlow) return;
        if (_readWriteAlphaTexturePrevious != null)
        {
            var alphaData = AlphaData.Create(_parameters, _readWriteAlphaTexturePrevious);
            DonnéesAlpha donnéesAlpha = new(_previousImageId, alphaData, GroupSize, _previousBytesBackgroundGroups, DateTime.Now);
            LeProjet.DonnéesAlphaStore.Upsert(donnéesAlpha);
        }
    }
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposedValue)
        {
            if (disposing)
            {
                // TODO: supprimer l'état managé (objets managés)
                _inferenceSession?.Dispose();
                _d3D12CommandQueue?.Dispose();
                _d3D12CommandAllocator?.Dispose();
                _d3D12CommandList?.Dispose();
                _d3D12CopyFence?.Dispose();
                _readWriteInputTexture?.Dispose();
                _readWriteInputBuffer?.Dispose();
                _readWriteAlphaTexturePreviousPrevious?.Dispose();
                _readWriteAlphaTexture?.Dispose();
                _readWriteAlphaTexturePrevious?.Dispose();
                _readWriteAlphaBuffer?.Dispose();
                _readWriteDebugTexture?.Dispose();
                _readWriteBackgroundGroupsTexture?.Dispose();
                _readWriteBackgroundGroupsHTexture?.Dispose();
                _readWriteBackgroundGroupsVTexture?.Dispose();
                _readWriteBackgroundGroupsFinalTexture?.Dispose();
                _readBackBackgroundGroupsFinalTexture?.Dispose();
                _readWriteBufferGroupInfos?.Dispose();
                _source?.Dispose();
                _alpha?.Dispose();
                foreach (nint res in _dmlResourcesToFree)
                    FreeGPUAllocation(res);
                _sharedD3D12ReadWriteTexture?.Dispose();
                _mFd3D11SharedResourceReadWriteTexture?.Dispose();
                _lecteurFichierMédia?.Dispose();
                _tJCompressor?.Dispose();
            }

            // TODO: libérer les ressources non managées (objets non managés) et substituer le finaliseur
            // TODO: affecter aux grands champs une valeur null
            if (_handleSharedResourceReadWriteTexture != -1)
            {
                DxUtils.CloseHandle(_handleSharedResourceReadWriteTexture);
                _handleSharedResourceReadWriteTexture = -1;
            }
            _disposedValue = true;
        }
    }

    // TODO: substituer le finaliseur uniquement si 'Dispose(bool disposing)' a du code pour libérer les ressources non managées
    ~EnginSuppresseurFond()
    {
        // Ne changez pas ce code. Placez le code de nettoyage dans la méthode 'Dispose(bool disposing)'
        Dispose(disposing: false);
    }

    public void Dispose()
    {
        // Ne changez pas ce code. Placez le code de nettoyage dans la méthode 'Dispose(bool disposing)'
        Dispose(disposing: true);
        GC.SuppressFinalize(this);
    }
}
