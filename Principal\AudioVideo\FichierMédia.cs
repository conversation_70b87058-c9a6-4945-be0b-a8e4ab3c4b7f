﻿using AndroidWebCam;
using MfEav.AndroidWebCamClient;
using Ceras;
using Ceras.Exceptions;
using DirectX;
using DocumentFormat.OpenXml.Drawing.Charts;
using DocumentFormat.OpenXml.Office2013.Excel;
using MediaFoundation;
using MediaFoundation.Misc;
using MfEav;
using Principal;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using Utils;
using Vortice.Direct2D1;
using Vortice.Direct3D11;
using Peracto.Svg.Types;
using ExCSS;
using Vanara.PInvoke;
using DocumentFormat.OpenXml.Wordprocessing;
using static FullFileReaderAPI;

namespace AudioVidéo;

class InstanceLecteurFichierMédia
{
    public FichierMédia FichierMédia;
    public string Demandeur;
    public LecteurFichierMédia LecteurFichierMédia;
    volatile public bool EstEnCoursDeLecture;
    public DateTime TempsDernièreUtilisation;
}
public partial class FichierMédia
{
    [Include] int _id = -1;
    public int Id => _id;

    [Include] public string CheminRelatif;
    public string CheminFichier => Path.Join(LeProjet.Répertoire, CheminRelatif);
    [Include] public bool AvecAudio;
    [Include] public bool AvecVidéo;
    [Include] public int LargeurImagesOriginales;
    [Include] public int HauteurImagesOriginales;

    bool _enEnregistrement;

    static object s_objectLockLecteursFichiersMédias = new();
    static List<InstanceLecteurFichierMédia> s_instancesLecteursFichiersMédias = new();

    static FichierMédia()
    {
        RessourcesStatiquesLibérables.Ajouter(LibérerRessourcesStatiques);
    }
    public FichierMédia()
    {
        if (_id == -1 && LeProjet != null) _id = LeProjet.NextFichierMédiaId++;
    }

    /// <summary>
    /// Ne pas appeler directement, utiliser plutôt LaGestionAudioVidéo.EnregistrerAsync().
    /// </summary>
    /// <param name="devices"></param>
    /// <param name="audio"></param>
    /// <param name="nombreCanaux"></param>
    /// <param name="vidéo"></param>
    /// <returns></returns>
    public async Task<Conteneur> EnregistrerAsync(Devices devices, int nombreCanaux, Fraction frameRateDésiré, bool vidéo)
    {
        var firstVideoSampleTime = TimeSpan.MinValue;
        var totalVideoDuration = TimeSpan.Zero;
        var totalAudioDuration = TimeSpan.Zero;
        int rotation = 0;
        List<float[][]> AudioFrames = new();

        bool VidéoAndroid = vidéo && devices.ActualCameraType == EnumCameraType.Android;
        bool VideoSystem = vidéo && devices.ActualCameraType == EnumCameraType.System;
        IMFMediaType mFMediaTypeAndroidVideoInput = null;
        IMFMediaType mediaTypeOutputVideoEncoder = null;
        Encoder videoEncoder = null;
        AudioEncoderParameters audioEncoderParameters = null;
        bool audio = nombreCanaux != 0;

        Debug.Assert(devices.NumberOfChannels == nombreCanaux); // Important car le nombre de canaux d'enregistrement sur disque doit correspondre à nombre de canaux envoyés par Devices.

        FenêtreProgressionEnregistrement fenêtreProgressionEnregistrement = new(audio, vidéo, VidéoAndroid);
        fenêtreProgressionEnregistrement.Show();

        try
        {
            Conteneur conteneur = await Task.Run(async () =>
            {
                try
                {
                    if (vidéo)
                    {
                        Stopwatch sw = Stopwatch.StartNew();
                        if (VidéoAndroid)
                        {
                            // Firs check if Android camera has started recording.
                            while (TcpReceiver.CurrentParameterSets == null && sw.ElapsedMilliseconds < 250) await Task.Delay(5);
                            if (TcpReceiver.CurrentParameterSets == null) return null;

                            MFExtern.MFCreateMediaType(out mFMediaTypeAndroidVideoInput).CheckHR();
                            mFMediaTypeAndroidVideoInput.SetGUID(MFAttributesClsid.MF_MT_MAJOR_TYPE, MFMediaType.Video).CheckHR();
                            WebCamParameters webCamParameters = TcpReceiver.CurrentWebCamParameters;
                            var mfMediaType = webCamParameters.Encoder == EnumEncoder.HEVC ? MFMediaType.HEVC : MFMediaType.H264;
                            mFMediaTypeAndroidVideoInput.SetGUID(MFAttributesClsid.MF_MT_SUBTYPE, mfMediaType).CheckHR();
                            MFExtern.MFSetAttributeSize(mFMediaTypeAndroidVideoInput, MFAttributesClsid.MF_MT_FRAME_SIZE, webCamParameters.CameraWidth, webCamParameters.CameraHeight).CheckHR();
                            MFExtern.MFSetAttributeRatio(mFMediaTypeAndroidVideoInput, MFAttributesClsid.MF_MT_FRAME_RATE, (int)(webCamParameters.FrameRate * 10000000), (int)10000000).CheckHR();
                            //MFExtern.MFSetAttributeRatio(mFMediaTypeAndroidVideoInput, MFAttributesClsid.MF_MT_FRAME_RATE, 30, 1).CheckHR();
                            MFExtern.MFSetAttributeRatio(mFMediaTypeAndroidVideoInput, MFAttributesClsid.MF_MT_PIXEL_ASPECT_RATIO, 1, 1).CheckHR();
                            mFMediaTypeAndroidVideoInput.SetUINT32(MFAttributesClsid.MF_MT_AVG_BITRATE, webCamParameters.BitRate).CheckHR();
                            mFMediaTypeAndroidVideoInput.SetUINT32(MFAttributesClsid.MF_MT_INTERLACE_MODE, (int)MFVideoInterlaceMode.Progressive).CheckHR();
                            rotation = TcpReceiver.CurrentRotation;
                            mFMediaTypeAndroidVideoInput.SetUINT32(MFAttributesClsid.MF_MT_VIDEO_ROTATION, rotation);
                            mFMediaTypeAndroidVideoInput.SetBlob(MFAttributesClsid.MF_MT_MPEG_SEQUENCE_HEADER, TcpReceiver.CurrentParameterSets, TcpReceiver.CurrentParameterSets.Length);
                            mediaTypeOutputVideoEncoder = mFMediaTypeAndroidVideoInput;
                        }
                        else
                        {
                            VideoEncoderParameters videoEncoderParameters = new()
                            {
                                CommonRateControlMode = EAVEncCommonRateControlMode.eAVEncCommonRateControlMode_Quality,
                                ContentType = EAVEncVideoContentType.eAVEncVideoContentType_FixedCameraAngle,
                                FrameHeight = LeProjet.PropriétésCaptureMédia.CameraMediaTypeInfosActuel.Height,
                                FrameRate = LeProjet.PropriétésCaptureMédia.CameraMediaTypeInfosActuel.FrameRate,
                                FrameWidth = LeProjet.PropriétésCaptureMédia.CameraMediaTypeInfosActuel.Width,
                                GuidInputSubType = MFMediaType.NV12,
                                GuidOutputSubType = LeProjet.PropriétésCaptureMédia.Encodeur != EnumEncoder.HEVC ? MFMediaType.H264 : MFMediaType.HEVC,
                                MaxGOPSize = (int)(LeProjet.PropriétésCaptureMédia.ÉcartImagesClées * LeProjet.PropriétésCaptureMédia.CameraMediaTypeInfosActuel.FrameRate),
                                NumberOfThreads = NumberOfCores >= 8 ? 8 : NumberOfCores >= 4 ? NumberOfCores - 1 : NumberOfCores, // NB: MF bug si plus que 8!
                                Quality = LeProjet.PropriétésCaptureMédia.QualitéQPEncodageCaméra,
                                QualityVsSpeed = LeProjet.PropriétésCaptureMédia.QualitéEncodageCaméra
                            };
                            videoEncoder = new Encoder(videoEncoderParameters);
                            videoEncoder.StartVideoEncoder(videoEncoderParameters, out mediaTypeOutputVideoEncoder);
                        }
                    }
                    if (audio)
                    {
                        AudioFrames = new List<float[][]>();
                        audioEncoderParameters = new()
                        {
                            GuidInputSubType = MFMediaType.PCM,
                            GuidOutputSubType = MFMediaType.AAC,
                            NumberOfChannels = nombreCanaux,
                            SampleRate = SampleRate
                        };
                    }
                    CheminRelatif = Path.Combine(LeProjet.RépertoireRelatifEnregistrements, $"{DateTime.Now:yyyyMMdd-HHmmss}.mp4");
                    using Writer writer = new();
                    if (!writer.OpenForPreencodedVideoSamples(CheminFichier, mediaTypeOutputVideoEncoder, audioEncoderParameters))
                        return null;
                    int totalKilobytes = 0;
                    _enEnregistrement = true;
                    LaGestionAudioVidéo.LesDevices.StartRecording(); // We call this just before we are ready to take the samples, otherwise the input sample queue might overflow!
                    bool overflow = false;
                    TimeSpan videoSampleOriginStartTime = default;
                    TimeSpan videoSampleTime = default;
                    TimeSpan audioSampleTime = default;
                    bool videoStarted = false;
                    while (_enEnregistrement && (!VideoSystem || !videoEncoder.DrainCompleted) && (VideoSystem || !fenêtreProgressionEnregistrement.Arrêter) && !overflow) // Attention, la logique du test sur la fenêtre est bonne et précise, attention en considérant qu'on semble faire le même test dans les lignes qui suivent, car ici le test ne concerne que le cas où il n'y a pas de vidéo provenant d'une caméra système nécessitant en décodeur qui doit terminé avant la fin du muxage de ses données...
                    {
                        devices.WaitForData();
                        if (VideoSystem)
                        {
                            if (fenêtreProgressionEnregistrement.Arrêter) videoEncoder.SignalEndOfStream();
                            if (devices.VideoSamplesForRecording.TryDequeue(out DeviceSample videoSample))
                            {
                                var sampleTime = videoSample.MfSample.GetTime();
                                if (!videoStarted)
                                {
                                    MFError.ThrowExceptionForHR(videoSample.MfSample.SetUINT32(MFAttributesClsid.MFSampleExtension_Discontinuity, 1));
                                    videoSampleOriginStartTime = sampleTime;
                                }
                                videoSampleTime = sampleTime - videoSampleOriginStartTime;
                                videoSample.MfSample.SetTime(videoSampleTime);
                                //WriteDebug($"*** Video sample received: {videoSampleTime.TotalMilliseconds}. Duration: {duration}. Waiting: {devices.VideoSamplesForRecording.Count}!");
                                if (videoEncoder.AddSampleToProcess(videoSample.MfSample) != HResult.S_OK) return null;
                                videoStarted = true;
                            }
                            if (!videoEncoder.OutputSamples.IsEmpty)
                            {
                                videoEncoder.OutputSamples.TryDequeue(out IMFSample outSample);
                                TimeSpan videoSampleTimeT = outSample.GetTime();
                                //DébogageGlobal.Add($"* Video sample encoded: {videoSampleTimeT}. Duration: {duration}.");
                                if (firstVideoSampleTime == TimeSpan.MinValue) firstVideoSampleTime = videoSampleTimeT;
                                if (videoSampleTimeT > totalVideoDuration) totalVideoDuration = videoSampleTimeT;
                                writer.WriteVideoSample(outSample);
                                totalKilobytes += outSample.GetByteBufferLength() / 1000;
                                MFHelpers.SafeRelease(outSample);
                            }
                        }   
                        else if (VidéoAndroid)
                        {
                            if (devices.VideoSamplesForRecording.TryDequeue(out DeviceSample videoSample))
                            {
                                var sampleTime = videoSample.MfSample.GetTime();
                                if (!videoStarted)
                                {
                                    MFError.ThrowExceptionForHR(videoSample.MfSample.SetUINT32(MFAttributesClsid.MFSampleExtension_Discontinuity, 1));
                                    videoSampleOriginStartTime = sampleTime;
                                }
                                videoSampleTime = sampleTime - videoSampleOriginStartTime;
                                videoSample.MfSample.SetTime(videoSampleTime);
                                if (firstVideoSampleTime == TimeSpan.MinValue) firstVideoSampleTime = videoSampleTime;
                                if (videoSampleTime > totalVideoDuration) totalVideoDuration = videoSampleTime;
                                writer.WriteVideoSample(videoSample.MfSample);
                                //DébogageGlobal.Add($"{DateTime.Now:HH:mm:ss.fff}. Written Video Sample. Time: {videoSampleTime}. Duration: {duration}.");
                                totalKilobytes += videoSample.MfSample.GetByteBufferLength() / 1000;
                                videoSample.Dispose();
                                videoStarted = true;
                            }
                        }
                        
                        if (audio && audioSampleTime <= videoSampleTime +
                            TimeSpan.FromMilliseconds(50) && // NB: Don't send audio samples to the writer if the video frames are lagging too much...
                            devices.AudioSamplesForRecording.TryDequeue(out DeviceSample audioSample))
                        {
                            if (!vidéo || videoStarted) // We start audio recording only after video recording has started in order to compensate for the time the camera takes to start.
                            {
                                var duration = audioSample.MfSample.GetDuration();
                                try
                                {
                                    float[][] audioFrame = audioSample.MfSample.GetAudioFrame(devices.NumberOfChannels, nombreCanaux, out float maximum);
                                    AudioFrames.Add(audioFrame);
                                    audioSample.MfSample.SetTime(audioSampleTime);
                                    //General.WriteDebug($"Cache count: {devices.AudioSamplesForRecording.Count}.  Audio sample: time: {adjustedTime.TotalSeconds}, duration: {duration}. length: {audioSample.MfSample.GetByteBufferLength()}. Video sample time: {videoSampleTime}.");
                                    if (audioSampleTime > totalAudioDuration) totalAudioDuration = audioSampleTime;
                                    writer.WriteAudioSample(audioSample.MfSample);
                                    //DébogageGlobal.Add($"{DateTime.Now:HH:mm:ss.fff}. Written Audio Sample. Time: {adjustedTime.TotalSeconds}. Duration: {duration}.");
                                    audioSampleTime += duration;
                                    totalKilobytes += audioSample.MfSample.GetByteBufferLength() / 1000;
                                    //General.WriteDebug($"Sample dequeued from microphone! Giving it time: {audioSampleTime}. # video waiting: {devices.VideoSamplesForRecording.Count}");
                                }
                                catch (Exception ex)
                                {
                                    General.WriteDebug(ex.Message);
                                    Debugger.Break();
                                }
                            }
                            audioSample.Dispose();
                            fenêtreProgressionEnregistrement.VolumeActuel = Math.Max(devices.CurrentAudioVolumes[0], devices.CurrentAudioVolumes.Length > 1 ? devices.CurrentAudioVolumes[1] : 0);
                        }
                        fenêtreProgressionEnregistrement.Durée = audio ? (float)audioSampleTime.TotalSeconds : (float)videoSampleTime.TotalSeconds;
                        //fenêtreProgressionEnregistrement.KilooctetsParSeconde = totalKilobytes / (float)audioSampleTime.TotalSeconds;
                        fenêtreProgressionEnregistrement.AudioCompressionLag = devices.AudioCompressionLag;
                        fenêtreProgressionEnregistrement.VideoCompressionLag = devices.VideoCompressionLag;
                        //if (devices.VideoCompressionLag == 0) Debug.WriteLine("0000000");
                        fenêtreProgressionEnregistrement.VideoTransmissionLag = devices.VideoTransmissionLag;
                        if (devices.AudioCompressionLag >= 100)
                        {
                            AjouterAvertissement("L'encodeur audio ne fournissait pas et des données audio ont été perdues.");
                            overflow = true;
                        }
                        else if (devices.VideoCompressionLag >= 100)
                        {
                            AjouterAvertissement("L'encodeur vidéo ne fournissait pas et des images ont été perdues. Veuillez diminuer la résolution de la caméra ou la qualité de l'encodage, ou encore changer le format d'encodage dans la configuration du logiciel s.v.p.");
                            overflow = true;
                        }
                        else if (devices.VideoTransmissionLag >= 100)
                        {
                            AjouterAvertissement("La vitesse de transmission entre le téléphone et l'ordinateur était trop lente pour la qualité requise des images de prévisualition ou des images encodées. Veuillez corriger s.v.p.");
                            overflow = true;
                        }
                        else if (devices.VideoTransmissionError)
                        {
                            AjouterAvertissement("Une erreur est survenu dans la transmission du téléphone à l'ordinateur et une image a été perdue.");
                            overflow = true;
                        }
                        else if (devices.FramesLostAtSourceError)
                        {
                            AjouterAvertissement("Une ou des images ont été perdues au niveau de la caméra.");
                            overflow = true;
                        }
                    }
                    if (totalKilobytes != 0) writer.TerminateFile(); // NB: Il ne faut terminer que si quelque chose a été écrit, sinon une exception est produite par MF.
                    if (overflow) return null;
                    Fraction frameRateOriginal;
                    if (vidéo)
                    {
                        frameRateOriginal = LeProjet.PropriétésCaptureMédia.CameraMediaTypeInfosActuel.FrameRate;
                        LargeurImagesOriginales = (int)LeProjet.PropriétésCaptureMédia.CameraMediaTypeInfosActuel.Width;
                        HauteurImagesOriginales = (int)LeProjet.PropriétésCaptureMédia.CameraMediaTypeInfosActuel.Height;
                        if (rotation == 90 || rotation == 270) (LargeurImagesOriginales, HauteurImagesOriginales) = (HauteurImagesOriginales, LargeurImagesOriginales);
                    }
                    else 
                    {
                        frameRateOriginal = frameRateDésiré;
                        LargeurImagesOriginales = 0;
                        HauteurImagesOriginales = 0;
                    }
                    AvecAudio = audio;
                    AvecVidéo = vidéo;
                    return new Conteneur(EnumUtilisation.Sauvegardé, this, totalVideoDuration, totalAudioDuration, firstVideoSampleTime, nombreCanaux, AudioFrames, frameRateDésiré, EnumTypeDonnées.AudioVidéoCorrectionsMarqueurs, améliorer: true);
                }
                catch (Exception ex)
                {
                    General.WriteDebug(ex.Message);
                    Debugger.Break();
                    return null;
                }
                finally
                {
                    LaGestionAudioVidéo.LesDevices.StopRecording();
                    videoEncoder?.Dispose();
                    MFHelpers.SafeRelease(ref mediaTypeOutputVideoEncoder);
                }
            });
            return conteneur;
        }
        finally
        {
            fenêtreProgressionEnregistrement.Close();
        }
    }
    public void ArrêterEnregistrement()
    {
        _enEnregistrement = false;
    }
    public async Task<Conteneur> LireFichierAsync(string cheminRelatif)
    {
        CheminRelatif = cheminRelatif;
        using FenêtreProgression fenêtreProgression = new(false);
        fenêtreProgression.Show();
        try
        {
            Progress<float> progress = new(pourcentage =>
            {
                fenêtreProgression.SetPourcentage(pourcentage);
            });
            Conteneur conteneur = await Task.Run(() =>
            {
                try
                {
                    if (!ReadEntireFileWithFullFileReader(CheminFichier, true, true, out Fraction FrameRateOriginal,
                                          out LargeurImagesOriginales, out HauteurImagesOriginales, out TimeSpan firstVideoSampleTime, out TimeSpan totalVideoDuration, out TimeSpan totalAudioDuration, SampleRate, -1, out int NombreCanauxSonores, out List<float[][]> audioFrames, progress, fenêtreProgression.CancellationToken)) return null;
                    AvecAudio = NombreCanauxSonores > 0;
                    AvecVidéo = LargeurImagesOriginales > 0;
                    return new Conteneur(EnumUtilisation.Sauvegardé, this, totalVideoDuration, totalAudioDuration, firstVideoSampleTime, NombreCanauxSonores, audioFrames, FrameRateOriginal, EnumTypeDonnées.Tout, améliorer: true);
                }
                catch (OperationCanceledException)
                {
                    return null;
                }
            });
            return conteneur;
        }
        finally
        {
            fenêtreProgression.Close();
        }
    }

    private static bool ReadEntireFileWithFullFileReader(string file, bool video, bool audio, out Fraction frameRate,
                                out int frameWidth, out int frameHeight, out TimeSpan firstVideoSampleTime,
                                out TimeSpan totalVideoDuration, out TimeSpan totalAudioDuration,
                                int sampleRate, int restrictionNumberOfChannels, out int numberOfChannels,
                                out List<float[][]> audioFrames,
                                IProgress<float> progress = null, CancellationToken? ct = null)
    {
        frameRate = new(-1, -1);
        frameWidth = -1;
        frameHeight = -1;
        numberOfChannels = -1;
        audioFrames = null;
        totalVideoDuration = TimeSpan.Zero;
        totalAudioDuration = TimeSpan.Zero;
        firstVideoSampleTime = TimeSpan.MinValue;

        try
        {
            // Create progress callback wrapper
            FullFileReaderAPI.ProgressCallback progressCallback = null;
            if (progress != null)
            {
                progressCallback = (float progressValue, IntPtr userData) =>
                {
                    progress.Report(progressValue);
                    ct?.ThrowIfCancellationRequested();
                };
            }

            if (!FullFileReader.ReadEntireFile(file, video, audio, out List<EncodedVideoSample> videoSamples,
                                             out List<AudioFrameData> audioFrameData, out FullFileReaderInfo fileInfo))
                return false;

            // Extract video information
            if (video && fileInfo.videoWidth > 0)
            {
                frameWidth = fileInfo.videoWidth;
                frameHeight = fileInfo.videoHeight;
                frameRate = new(fileInfo.frameRateNum, fileInfo.frameRateDen);
                totalVideoDuration = TimeSpan.FromSeconds(fileInfo.videoDuration);
                firstVideoSampleTime = TimeSpan.FromSeconds(fileInfo.firstVideoTimestamp);
            }

            // Extract audio information and convert to float[][]
            if (audio && fileInfo.audioChannels > 0)
            {
                numberOfChannels = restrictionNumberOfChannels != -1 ? restrictionNumberOfChannels : fileInfo.audioChannels;
                totalAudioDuration = TimeSpan.FromSeconds(fileInfo.audioDuration);
                audioFrames = ConvertAudioFrameDataToFloatArrays(audioFrameData, numberOfChannels, sampleRate);
            }
            else
            {
                audioFrames = new List<float[][]>();
            }

            return true;
        }
        catch (OperationCanceledException)
        {
            throw;
        }
        catch (Exception)
        {
            return false;
        }
    }

    private static List<float[][]> ConvertAudioFrameDataToFloatArrays(List<AudioFrameData> audioFrameData, int numberOfChannels, int targetSampleRate)
    {
        List<float[][]> audioFrames = new List<float[][]>();

        foreach (var frameData in audioFrameData)
        {
            if (frameData.channelCount == 0 || frameData.channels == IntPtr.Zero)
                continue;

            // Get channel sizes
            int[] channelSizes = new int[frameData.channelCount];
            Marshal.Copy(frameData.channelSizes, channelSizes, 0, frameData.channelCount);

            // Assume all channels have the same size (typical for audio)
            int samplesPerChannel = channelSizes[0] / sizeof(float); // Assuming float samples

            float[][] channelData = new float[numberOfChannels][];

            for (int ch = 0; ch < numberOfChannels; ch++)
            {
                channelData[ch] = new float[samplesPerChannel];

                // Get pointer to channel data
                IntPtr channelPtr = Marshal.ReadIntPtr(frameData.channels, ch * IntPtr.Size);

                if (ch < frameData.channelCount && channelPtr != IntPtr.Zero)
                {
                    // Copy float data from native memory
                    Marshal.Copy(channelPtr, channelData[ch], 0, samplesPerChannel);
                }
                // If we need more channels than available, the extra channels remain silent (zero)
            }

            audioFrames.Add(channelData);
        }

        return audioFrames;
    }

    /// <summary>
    /// Charge une image dans le lecteur de fichier média. Si le lecteur de fichier média est déjà en cours d'utilisation, il va en créer un autre. Le paramètre demandeur est utilisé pour ne pas mélanger les lecteurs provenant de différentes sources, ce qui ferait en sorte qu'un lecteur pourrait avancer et reculer selon l'appelant...
    /// </summary>
    /// <param name="demandeur"></param>
    /// <param name="imageVidéo"></param>
    /// <param name="hauteRésolution"></param>
    public void ChargerImage(string demandeur, ImageVidéo imageVidéo, bool hauteRésolution = false)
    {
        int diviseur = LaConfiguration.DiviseurRésolutionRenduVidéoUI;
        int largeurImages = hauteRésolution ? LargeurImagesOriginales : LargeurImagesOriginales / LaConfiguration.DiviseurRésolutionRenduVidéoUI;
        int hauteurImages = hauteRésolution ? HauteurImagesOriginales : HauteurImagesOriginales / LaConfiguration.DiviseurRésolutionRenduVidéoUI;
        while (largeurImages % 2 == 1)
        {
            diviseur /= 2;
            largeurImages = hauteRésolution ? LargeurImagesOriginales : LargeurImagesOriginales / diviseur;
            hauteurImages = hauteRésolution ? HauteurImagesOriginales : HauteurImagesOriginales / diviseur;
        }
        InstanceLecteurFichierMédia instance = null;
        lock (s_objectLockLecteursFichiersMédias)
        {
            for (int i = 0; i < s_instancesLecteursFichiersMédias.Count; i++)
            {
                var instanceTemp = s_instancesLecteursFichiersMédias[i];
                if (!instanceTemp.EstEnCoursDeLecture && (DateTime.Now - instanceTemp.TempsDernièreUtilisation).TotalSeconds > 5)
                {
                    //Debug.WriteLine($"INSTANCE OUTDATED by {(DateTime.Now - instanceTemp.TempsDernièreUtilisation).TotalSeconds} seconds!");
                    instanceTemp.LecteurFichierMédia.Dispose();
                    s_instancesLecteursFichiersMédias.Remove(instanceTemp);
                    i--;
                }
            }
            foreach (var instanceTemp in s_instancesLecteursFichiersMédias)
            {
                if (instanceTemp.Demandeur == demandeur && instanceTemp.FichierMédia == this && !instanceTemp.EstEnCoursDeLecture && instanceTemp.LecteurFichierMédia.LargeurImages == largeurImages && instanceTemp.LecteurFichierMédia.HauteurImages == hauteurImages) 
                {
                    instance = instanceTemp;
                    instance.EstEnCoursDeLecture = true;
                    instance.TempsDernièreUtilisation = DateTime.Now;
                    //General.WriteDebug($"INSTANCE REUSED! Total instance count: {s_instancesLecteursFichiersMédias.Count}");
                    break;
                }
            }
            if (instance == null)
            {
                LecteurFichierMédia lecteur = new(this, largeurImages, hauteurImages, false); // false = NV12 format (not transparent)
                instance = new() { FichierMédia = this, Demandeur = demandeur, LecteurFichierMédia = lecteur, EstEnCoursDeLecture = true, TempsDernièreUtilisation = DateTime.Now };
                s_instancesLecteursFichiersMédias.Add(instance);
                //General.WriteDebug($"NEW INSTANCE CREATED! Total instance count: {s_instancesLecteursFichiersMédias.Count}");
            }
        }
        instance.LecteurFichierMédia.LireSample(imageVidéo);
        lock (s_objectLockLecteursFichiersMédias)
        {
            instance.EstEnCoursDeLecture = false;
            instance.TempsDernièreUtilisation = DateTime.Now;
        }
    }
    public static void LibérerRessourcesStatiques()
    {
        //General.WriteDebug($"Libération d'instances de lecteurs fichier média! Total instance count: {s_instancesLecteursFichiersMédias.Count}");
        lock (s_objectLockLecteursFichiersMédias)
        {
            foreach (var instance in s_instancesLecteursFichiersMédias)
                instance.LecteurFichierMédia.Dispose();
            s_instancesLecteursFichiersMédias.Clear();
        }
    }
}

