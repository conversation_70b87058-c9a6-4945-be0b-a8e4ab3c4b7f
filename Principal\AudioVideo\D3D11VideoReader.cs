﻿using System;
using System.Runtime.InteropServices;
using Vortice.Direct3D11;
using Vortice.DXGI;

namespace ImageMatter.Video
{
    /// <summary>
    /// C# wrapper for D3D11VideoReader using Vortice.Direct3D11
    /// </summary>
    public class D3D11VideoReader : IDisposable
    {
        #region Native Interop

        // Video format enum
        public enum VideoFormat
        {
            NV12 = 0,                   // NV12 format from NV12VideoReader
            BgraTransparent = 1,        // BGRA format from BgraVideoReader (transparent frames)
            Bgra = 2                    // BGRA format from NV12VideoReader converted to BGRA
        }

        // Error codes
        public enum ErrorCode
        {
            Success = 0,
            InvalidParameter = -1,
            InitializationFailed = -2,
            FileNotFound = -3,
            SeekFailed = -4,
            ReadFailed = -5,
            TextureInvalid = -6,
            CudaFailed = -7,
            Unknown = -99
        }

        // Video properties structure
        [StructLayout(LayoutKind.Sequential)]
        public struct VideoProperties
        {
            public int Width;
            public int Height;
            public double Duration;        // in seconds
            public double FrameRate;       // frames per second
            public int TotalFrames;        // estimated total frames
        }

        // P/Invoke declarations
        [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int D3D11VideoReader_Create(
            IntPtr device,
            [MarshalAs(UnmanagedType.LPStr)] string filePath,
            out IntPtr outHandle);

        [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int D3D11VideoReader_CreateWithFormat(
            IntPtr device,
            [MarshalAs(UnmanagedType.LPStr)] string filePath,
            VideoFormat format,
            out IntPtr outHandle);

        [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int D3D11VideoReader_CreateWithMode(
            IntPtr device,
            [MarshalAs(UnmanagedType.LPStr)] string filePath,
            int transparentFrames,
            out IntPtr outHandle);

        [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern void D3D11VideoReader_Destroy(IntPtr handle);

        [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int D3D11VideoReader_GetProperties(
            IntPtr handle,
            out VideoProperties outProperties);

        [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int D3D11VideoReader_GetWidth(IntPtr handle);

        [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int D3D11VideoReader_GetHeight(IntPtr handle);

        [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern double D3D11VideoReader_GetDuration(IntPtr handle);

        [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern double D3D11VideoReader_GetFrameRate(IntPtr handle);

        [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern VideoFormat D3D11VideoReader_GetCurrentFormat(IntPtr handle);

        [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int D3D11VideoReader_TransparentFilesExist(
            [MarshalAs(UnmanagedType.LPStr)] string basePath);

        [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int D3D11VideoReader_Seek(IntPtr handle, double timeInSeconds);

        [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int D3D11VideoReader_ReadFrame(
            IntPtr handle,
            IntPtr texture,
            out double outTimestamp);

        [DllImport("ImageMatterLib.dll", CallingConvention = CallingConvention.Cdecl)]
        private static extern int D3D11VideoReader_ReadFrameWithResize(
            IntPtr handle,
            IntPtr texture,
            int width,
            int height,
            out double outTimestamp);

        #endregion

        #region Fields and Properties

        private IntPtr _handle = IntPtr.Zero;
        private bool _disposed = false;
        private readonly ID3D11Device1 _device;

        /// <summary>
        /// Gets the video properties
        /// </summary>
        public VideoProperties Properties { get; private set; }

        /// <summary>
        /// Gets the video width
        /// </summary>
        public int Width => Properties.Width;

        /// <summary>
        /// Gets the video height
        /// </summary>
        public int Height => Properties.Height;

        /// <summary>
        /// Gets the video duration in seconds
        /// </summary>
        public double Duration => Properties.Duration;

        /// <summary>
        /// Gets the video frame rate
        /// </summary>
        public double FrameRate => Properties.FrameRate;

        /// <summary>
        /// Gets the total number of frames
        /// </summary>
        public int TotalFrames => Properties.TotalFrames;

        /// <summary>
        /// Gets the current video format
        /// </summary>
        public VideoFormat CurrentFormat { get; private set; }

        #endregion

        #region Constructors

        /// <summary>
        /// Creates a new D3D11VideoReader with automatic format detection
        /// </summary>
        /// <param name="device">Vortice D3D11 device</param>
        /// <param name="filePath">Path to the video file</param>
        public D3D11VideoReader(ID3D11Device1 device, string filePath)
        {
            _device = device ?? throw new ArgumentNullException(nameof(device));

            var result = D3D11VideoReader_Create(device.NativePointer, filePath, out _handle);
            CheckError(result, $"Failed to create D3D11VideoReader for file: {filePath}");

            InitializeProperties();
        }

        /// <summary>
        /// Creates a new D3D11VideoReader with specific format
        /// </summary>
        /// <param name="device">Vortice D3D11 device</param>
        /// <param name="filePath">Path to the video file</param>
        /// <param name="format">Video format to use</param>
        public D3D11VideoReader(ID3D11Device1 device, string filePath, VideoFormat format)
        {
            _device = device ?? throw new ArgumentNullException(nameof(device));

            var result = D3D11VideoReader_CreateWithFormat(device.NativePointer, filePath, format, out _handle);
            CheckError(result, $"Failed to create D3D11VideoReader for file: {filePath} with format: {format}");

            InitializeProperties();
        }

        /// <summary>
        /// Creates a new D3D11VideoReader with legacy mode selection
        /// </summary>
        /// <param name="device">Vortice D3D11 device</param>
        /// <param name="filePath">Path to the video file</param>
        /// <param name="transparentFrames">True for BGRA transparent frames, false for NV12 opaque frames</param>
        public D3D11VideoReader(ID3D11Device1 device, string filePath, bool transparentFrames)
        {
            _device = device ?? throw new ArgumentNullException(nameof(device));

            var result = D3D11VideoReader_CreateWithMode(device.NativePointer, filePath, transparentFrames ? 1 : 0, out _handle);
            CheckError(result, $"Failed to create D3D11VideoReader for file: {filePath} with transparent frames: {transparentFrames}");

            InitializeProperties();
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Checks if transparent video files exist for the given base path
        /// </summary>
        /// <param name="basePath">Base path to check</param>
        /// <returns>True if both RGB and Alpha video files exist</returns>
        public static bool TransparentFilesExist(string basePath)
        {
            return D3D11VideoReader_TransparentFilesExist(basePath) == 1;
        }

        /// <summary>
        /// Seeks to a specific time in the video
        /// </summary>
        /// <param name="timeInSeconds">Target time in seconds</param>
        public void Seek(double timeInSeconds)
        {
            ThrowIfDisposed();

            var result = D3D11VideoReader_Seek(_handle, timeInSeconds);
            CheckError(result, $"Failed to seek to time: {timeInSeconds}");
        }

        /// <summary>
        /// Reads the next frame into a D3D11 texture
        /// </summary>
        /// <param name="texture">Target texture (must be correct format and size)</param>
        /// <returns>Frame timestamp in seconds</returns>
        public double ReadFrame(ID3D11Texture2D texture)
        {
            ThrowIfDisposed();

            if (texture == null)
                throw new ArgumentNullException(nameof(texture));

            var result = D3D11VideoReader_ReadFrame(_handle, texture.NativePointer, out double timestamp);
            CheckError(result, "Failed to read frame");

            return timestamp;
        }

        /// <summary>
        /// Reads the next frame with optional resizing into a D3D11 texture
        /// </summary>
        /// <param name="texture">Target texture (must be correct format)</param>
        /// <param name="width">Target width for resizing (0 = no resize)</param>
        /// <param name="height">Target height for resizing (0 = no resize)</param>
        /// <returns>Frame timestamp in seconds</returns>
        public double ReadFrameWithResize(ID3D11Texture2D texture, int width = 0, int height = 0)
        {
            ThrowIfDisposed();

            if (texture == null)
                throw new ArgumentNullException(nameof(texture));

            var result = D3D11VideoReader_ReadFrameWithResize(_handle, texture.NativePointer, width, height, out double timestamp);
            CheckError(result, "Failed to read frame with resize");

            return timestamp;
        }

        /// <summary>
        /// Creates a compatible texture for this video reader
        /// </summary>
        /// <param name="usage">Texture usage flags</param>
        /// <param name="bindFlags">Texture bind flags</param>
        /// <param name="cpuAccessFlags">CPU access flags</param>
        /// <param name="miscFlags">Miscellaneous flags</param>
        /// <returns>Compatible texture</returns>
        public ID3D11Texture2D CreateCompatibleTexture(
            ResourceUsage usage = ResourceUsage.Default,
            BindFlags bindFlags = BindFlags.ShaderResource,
            CpuAccessFlags cpuAccessFlags = CpuAccessFlags.None,
            ResourceOptionFlags miscFlags = ResourceOptionFlags.None)
        {
            ThrowIfDisposed();

            var format = CurrentFormat switch
            {
                VideoFormat.NV12 => Format.NV12,
                VideoFormat.BgraTransparent or VideoFormat.Bgra => Format.B8G8R8A8_UNorm,
                _ => throw new InvalidOperationException($"Unknown video format: {CurrentFormat}")
            };

            var desc = new Texture2DDescription
            {
                Width = (uint)Width,
                Height = (uint)Height,
                MipLevels = 1,
                ArraySize = 1,
                Format = format,
                SampleDescription = new SampleDescription(1, 0),
                Usage = usage,
                BindFlags = bindFlags,
                CPUAccessFlags = cpuAccessFlags,
                MiscFlags = miscFlags
            };

            return _device.CreateTexture2D(desc);
        }

        /// <summary>
        /// Creates a compatible texture with custom dimensions
        /// </summary>
        /// <param name="width">Texture width</param>
        /// <param name="height">Texture height</param>
        /// <param name="usage">Texture usage flags</param>
        /// <param name="bindFlags">Texture bind flags</param>
        /// <param name="cpuAccessFlags">CPU access flags</param>
        /// <param name="miscFlags">Miscellaneous flags</param>
        /// <returns>Compatible texture</returns>
        public ID3D11Texture2D CreateCompatibleTexture(
            int width,
            int height,
            ResourceUsage usage = ResourceUsage.Default,
            BindFlags bindFlags = BindFlags.ShaderResource,
            CpuAccessFlags cpuAccessFlags = CpuAccessFlags.None,
            ResourceOptionFlags miscFlags = ResourceOptionFlags.None)
        {
            ThrowIfDisposed();

            var format = CurrentFormat switch
            {
                VideoFormat.NV12 => Format.NV12,
                VideoFormat.BgraTransparent or VideoFormat.Bgra => Format.B8G8R8A8_UNorm,
                _ => throw new InvalidOperationException($"Unknown video format: {CurrentFormat}")
            };

            var desc = new Texture2DDescription
            {
                Width = (uint)width,
                Height = (uint)height,
                MipLevels = 1,
                ArraySize = 1,
                Format = format,
                SampleDescription = new SampleDescription(1, 0),
                Usage = usage,
                BindFlags = bindFlags,
                CPUAccessFlags = cpuAccessFlags,
                MiscFlags = miscFlags
            };

            return _device.CreateTexture2D(desc);
        }

        #endregion

        #region Private Methods

        private void InitializeProperties()
        {
            var result = D3D11VideoReader_GetProperties(_handle, out VideoProperties props);
            CheckError(result, "Failed to get video properties");

            Properties = props;
            CurrentFormat = D3D11VideoReader_GetCurrentFormat(_handle);
        }

        private static void CheckError(int result, string message)
        {
            if (result != 0)
            {
                var errorCode = (ErrorCode)result;
                throw new D3D11VideoReaderException($"{message}. Error: {errorCode}", errorCode);
            }
        }

        private void ThrowIfDisposed()
        {
            if (_disposed)
                throw new ObjectDisposedException(nameof(D3D11VideoReader));
        }

        #endregion

        #region IDisposable Implementation

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (_handle != IntPtr.Zero)
                {
                    D3D11VideoReader_Destroy(_handle);
                    _handle = IntPtr.Zero;
                }
                _disposed = true;
            }
        }

        ~D3D11VideoReader()
        {
            Dispose(false);
        }

        #endregion
    }

    /// <summary>
    /// Exception thrown by D3D11VideoReader operations
    /// </summary>
    public class D3D11VideoReaderException : Exception
    {
        public D3D11VideoReader.ErrorCode ErrorCode { get; }

        public D3D11VideoReaderException(string message, D3D11VideoReader.ErrorCode errorCode)
            : base(message)
        {
            ErrorCode = errorCode;
        }

        public D3D11VideoReaderException(string message, D3D11VideoReader.ErrorCode errorCode, Exception innerException)
            : base(message, innerException)
        {
            ErrorCode = errorCode;
        }
    }
}

// Usage Examples:

/*
// Example 1: Basic usage with automatic format detection
using (var videoReader = new D3D11VideoReader(device, @"C:\Videos\myvideo.mp4"))
{
    Console.WriteLine($"Video: {videoReader.Width}x{videoReader.Height}, {videoReader.Duration:F2}s");
    
    // Create a compatible texture
    using (var texture = videoReader.CreateCompatibleTexture())
    {
        // Read frames
        for (int i = 0; i < 100; i++)
        {
            try
            {
                double timestamp = videoReader.ReadFrame(texture);
                Console.WriteLine($"Frame {i}: timestamp = {timestamp:F3}s");
            }
            catch (D3D11VideoReaderException ex) when (ex.ErrorCode == D3D11VideoReader.ErrorCode.ReadFailed)
            {
                // End of video
                break;
            }
        }
    }
}

// Example 2: Transparent video with specific format
if (D3D11VideoReader.TransparentFilesExist(@"C:\Videos\transparent_video.mp4"))
{
    using (var videoReader = new D3D11VideoReader(device, @"C:\Videos\transparent_video.mp4", D3D11VideoReader.VideoFormat.BgraTransparent))
    {
        // Seek to 5 seconds
        videoReader.Seek(5.0);
        
        // Create custom sized texture for resizing
        using (var texture = videoReader.CreateCompatibleTexture(1920, 1080))
        {
            double timestamp = videoReader.ReadFrameWithResize(texture, 1920, 1080);
            Console.WriteLine($"Resized frame timestamp: {timestamp:F3}s");
        }
    }
}

// Example 3: NV12 format with shared texture
using (var videoReader = new D3D11VideoReader(device, @"C:\Videos\nv12video.mp4", D3D11VideoReader.VideoFormat.NV12))
{
    // Create shared texture for interop
    using (var texture = videoReader.CreateCompatibleTexture(
        miscFlags: ResourceMiscFlags.SharedNthandle | ResourceMiscFlags.SharedKeyedmutex))
    {
        double timestamp = videoReader.ReadFrame(texture);
        Console.WriteLine($"Shared NV12 frame: {timestamp:F3}s");
    }
}
*/