﻿using DirectXControl;
using Svg;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using Vortice.Direct2D1;

namespace Commun;

public class ParserExtractSvgImages
{
    readonly TamponImagesBgra _tamponBitmaps;
    readonly Dictionary<string, ImageDirectX> _imagesVidéos;

    public ParserExtractSvgImages(TamponImagesBgra tamponBitmaps, Dictionary<string, ImageDirectX> imagesVideos)
    {
        _tamponBitmaps = tamponBitmaps;
        _imagesVidéos = imagesVideos;
    }

    public void ParseSVGElementFromEav(ID2D1DeviceContext6 dc, SvgElement element)
    {
        if (element.GetType().Name == "SvgImage")
        {
            SvgImage image = (SvgImage)element;
            if (image.TryGetAttribute("href", out string hRef))
            {
                Uri uri = new(hRef);
                switch (uri.Scheme)
                {
                    case "image":
                        {
                            if (int.TryParse(uri.LocalPath, out int idBitmap))
                            {
                                ImageBgra imageBgra = _tamponBitmaps.TrouverBitmap(idBitmap);
                                using MemoryStream memoryStream = new();
                                DxUtils.WriteImageToStream(dc, imageBgra, memoryStream);
                                using StreamReader streamReader = new(memoryStream);
                                string text = streamReader.ReadToEnd();
                                image.Href = $"data:{text}";
                            }
                        }
                        break;
                    case "video":
                        {
                            if (_imagesVidéos != null && // NB: ImagesVidéos might be null when doing tests.
                                _imagesVidéos.TryGetValue(uri.LocalPath, out ImageDirectX imageDirectX))
                            {
                                using MemoryStream memoryStream = new();
                                DxUtils.WriteImageToStream(dc, imageDirectX, memoryStream);
                                using StreamReader streamReader = new(memoryStream);
                                string text = streamReader.ReadToEnd();
                                image.CustomAttributes["data-video"] = uri.LocalPath;
                            }
                        }
                        break;
                    default:
                        break;
                }
            }
        }
        foreach (SvgElement child in element.Children)
            ParseSVGElementFromEav(dc, child);
    }
    /// <summary>
    /// Parses the images contained in the svg in order for an external svg software to edit it.
    /// </summary>
    public string ParseSVGDocumentFromEav(ID2D1DeviceContext6 dc, string svgData, out float width, out float height)
    {
        using MemoryStream stream0 = new(Encoding.UTF8.GetBytes(svgData));
        SvgDocument svgDocument = SvgDocument.Open<SvgDocument>(stream0);
        foreach (SvgElement child in svgDocument.Children)
            ParseSVGElementFromEav(dc, child);
        width = svgDocument.Width;
        height = svgDocument.Height;
        return svgDocument.GetXML();
    }
}
