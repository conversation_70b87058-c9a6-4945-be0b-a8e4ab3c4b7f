﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)\microsoft.visualstudio.threading.analyzers\17.13.61\build\Microsoft.VisualStudio.Threading.Analyzers.targets" Condition="Exists('$(NuGetPackageRoot)\microsoft.visualstudio.threading.analyzers\17.13.61\build\Microsoft.VisualStudio.Threading.Analyzers.targets')" />
    <Import Project="$(NuGetPackageRoot)\microsoft.extensions.logging.abstractions\7.0.0\buildTransitive\net6.0\Microsoft.Extensions.Logging.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)\microsoft.extensions.logging.abstractions\7.0.0\buildTransitive\net6.0\Microsoft.Extensions.Logging.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)\assimpnet\5.0.0-beta1\build\AssimpNet.targets" Condition="Exists('$(NuGetPackageRoot)\assimpnet\5.0.0-beta1\build\AssimpNet.targets')" />
    <Import Project="$(NuGetPackageRoot)\computesharp.core\3.2.0\buildTransitive\ComputeSharp.Core.targets" Condition="Exists('$(NuGetPackageRoot)\computesharp.core\3.2.0\buildTransitive\ComputeSharp.Core.targets')" />
    <Import Project="$(NuGetPackageRoot)\computesharp\3.2.0\buildTransitive\ComputeSharp.targets" Condition="Exists('$(NuGetPackageRoot)\computesharp\3.2.0\buildTransitive\ComputeSharp.targets')" />
    <Import Project="$(NuGetPackageRoot)\computesharp.d2d1\3.2.0\buildTransitive\ComputeSharp.D2D1.targets" Condition="Exists('$(NuGetPackageRoot)\computesharp.d2d1\3.2.0\buildTransitive\ComputeSharp.D2D1.targets')" />
    <Import Project="$(NuGetPackageRoot)\system.text.json\9.0.3\buildTransitive\net8.0\System.Text.Json.targets" Condition="Exists('$(NuGetPackageRoot)\system.text.json\9.0.3\buildTransitive\net8.0\System.Text.Json.targets')" />
    <Import Project="$(NuGetPackageRoot)\magick.net-q16-anycpu\14.5.0\buildTransitive\netstandard20\Magick.NET-Q16-AnyCPU.targets" Condition="Exists('$(NuGetPackageRoot)\magick.net-q16-anycpu\14.5.0\buildTransitive\netstandard20\Magick.NET-Q16-AnyCPU.targets')" />
    <Import Project="$(NuGetPackageRoot)\microsoft.ml.onnxruntime.managed\1.21.1\build\netstandard2.0\Microsoft.ML.OnnxRuntime.Managed.targets" Condition="Exists('$(NuGetPackageRoot)\microsoft.ml.onnxruntime.managed\1.21.1\build\netstandard2.0\Microsoft.ML.OnnxRuntime.Managed.targets')" />
    <Import Project="$(NuGetPackageRoot)\microsoft.ai.directml\1.15.4\build\Microsoft.AI.DirectML.targets" Condition="Exists('$(NuGetPackageRoot)\microsoft.ai.directml\1.15.4\build\Microsoft.AI.DirectML.targets')" />
    <Import Project="$(NuGetPackageRoot)\microsoft.ml.onnxruntime.directml\1.21.1\build\netstandard2.1\Microsoft.ML.OnnxRuntime.DirectML.targets" Condition="Exists('$(NuGetPackageRoot)\microsoft.ml.onnxruntime.directml\1.21.1\build\netstandard2.1\Microsoft.ML.OnnxRuntime.DirectML.targets')" />
  </ImportGroup>
</Project>