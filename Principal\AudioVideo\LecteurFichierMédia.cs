﻿using System;
using System.CodeDom;
using System.Collections.Generic;
using System.Diagnostics;
using System.Security.Cryptography;
using System.Text;
using DirectX;
using DocumentFormat.OpenXml.Drawing.Diagrams;
using MediaFoundation;
using MfEav;
using Principal;
using SharpGen.Runtime;
using Utils;
using Vortice.Direct3D11;
using ImageMatter.Video;
using Vanara.Extensions.Reflection;

namespace AudioVidéo
{
    public delegate void ProcessSample(ImageVidéo image, ID3D11Texture2D texture, int largeurImage, int hauteurImage);
    public class LecteurFichierMédia : IDisposable
    {
        public const int NormalMaxGopDistance = 2;
        public int Id; // For debugging purposes only.
        FichierMédia _fichierMédia;
        D3D11VideoReader _videoReader;

        int _largeurImages = -1;
        public int LargeurImages => _largeurImages;
        int _hauteurImages = -1;
        public int HauteurImages => _hauteurImages;
        bool _transparentFrames;

        public TimeSpan FirstReadFrameTime = TimeSpan.MinValue;
        ImageTexture _beforeLastFrameRead;
        double _beforeLastFrameTimestamp;
        public TimeSpan BeforeLastReadFrameTime => _beforeLastFrameRead == null ? TimeSpan.FromMinutes(0) : TimeSpan.FromSeconds(_beforeLastFrameTimestamp) - FirstReadFrameTime;
        ImageTexture _lastFrameRead;
        double _lastFrameTimestamp;
        public TimeSpan LastReadFrameTime => _lastFrameRead == null ? TimeSpan.FromMinutes(0) : TimeSpan.FromSeconds(_lastFrameTimestamp) - FirstReadFrameTime;

        public LecteurFichierMédia(FichierMédia fichierMédia, bool transparentFrames = false)
        {
            _fichierMédia = fichierMédia;
            _largeurImages = fichierMédia.LargeurImagesOriginales;
            _hauteurImages = fichierMédia.HauteurImagesOriginales;
            _transparentFrames = transparentFrames;
            Id = LeProjet == null ? 0 : LeProjet.NextFichierMédiaId++;
        }
        public LecteurFichierMédia(FichierMédia fichierMédia, int largeurImages = -1, int hauteurImages = -1, bool transparentFrames = false)
        {
            _fichierMédia = fichierMédia;
            _largeurImages = largeurImages == -1 ? fichierMédia.LargeurImagesOriginales : largeurImages;
            _hauteurImages = hauteurImages == -1 ? fichierMédia.HauteurImagesOriginales : hauteurImages;
            _transparentFrames = transparentFrames;
            Id = LeProjet == null ? 0 : LeProjet.NextFichierMédiaId++;
        }
        /// <summary>
        /// ATTENTION: The imageTexture returned must not be released!
        /// </summary>
        /// <param name="imageVidéo"></param>
        /// <param name="processSample"></param>
        /// <returns></returns>
        public ImageTexture LireSample(ImageVidéo imageVidéo)
        {
            if (_videoReader == null)
            {
                var device = MfEav.MFInitialization.DirectXParameters.D3D11Device;
                _videoReader = new D3D11VideoReader(device, _fichierMédia.CheminFichier, _transparentFrames);
                _videoReader.Seek(0.0); // Start at the beginning
            }
            if (FirstReadFrameTime == TimeSpan.MinValue)
            {
                var imageTexture = ImageTexturePool.PuiserImage(MfEav.MFInitialization.DirectXParameters, _videoReader.CurrentFormat == D3D11VideoReader.VideoFormat.NV12 ? Vortice.DXGI.Format.NV12 : Vortice.DXGI.Format.B8G8R8A8_UNorm, (uint)_largeurImages, (uint)_hauteurImages, bindFlags: BindFlags.ShaderResource, resourceOptionFlags: ResourceOptionFlags.SharedNTHandle | ResourceOptionFlags.SharedKeyedMutex);

                var timestamp = _videoReader.ReadFrame(imageTexture.RawTexture);
                if (timestamp < 0) return null;
                FirstReadFrameTime = TimeSpan.FromSeconds(timestamp);
                _lastFrameRead = imageTexture;
                _lastFrameTimestamp = timestamp;
            }
            bool scanAll = false;
            while (true)
            {
                if (_beforeLastFrameRead != null && imageVidéo.FrameTime >= BeforeLastReadFrameTime && imageVidéo.FrameTime <= LastReadFrameTime)
                {
                    //General.WriteDebug($"---- Before Last : {_beforeLastFrameTimestamp}. Last: {_lastFrameTimestamp}.");
                    if (Math.Abs((imageVidéo.FrameTime - BeforeLastReadFrameTime).TotalMicroseconds) < Math.Abs((LastReadFrameTime - imageVidéo.FrameTime).TotalMicroseconds))
                    {
                        //General.WriteDebug($"---- asked for: {imageVidéo.FrameTime}. Returns Before Last : {_beforeLastFrameTimestamp}");
                        return _beforeLastFrameRead;
                    }
                    else
                    {
                        //General.WriteDebug($"---- asked for: {imageVidéo.FrameTime}. Returns Last : {_lastFrameTimestamp}");

                        return _lastFrameRead;
                    }
                }
                ImageTexture imageTexture = null;
                double timestamp = 0.0;
                if (imageVidéo.FrameTime < BeforeLastReadFrameTime ||
                    (!scanAll && Math.Abs((imageVidéo.FrameTime - LastReadFrameTime).TotalSeconds) > NormalMaxGopDistance))
                {
                    TimeSpan timeSpanStartFrame = imageVidéo.FrameTime - TimeSpan.FromSeconds(0.001); // On part un peu en arrière de manière à nous assurer de lire le frame désiré....
                    if (timeSpanStartFrame < TimeSpan.Zero) timeSpanStartFrame = TimeSpan.Zero;
                    ReleaseFrames();
                    _videoReader.Seek(timeSpanStartFrame.TotalSeconds);
                    General.WriteDebug($"Reset position to {timeSpanStartFrame}");
                    imageTexture = ImageTexturePool.PuiserImage(MfEav.MFInitialization.DirectXParameters, _videoReader.CurrentFormat == D3D11VideoReader.VideoFormat.NV12 ? Vortice.DXGI.Format.NV12 : Vortice.DXGI.Format.B8G8R8A8_UNorm, (uint)_largeurImages, (uint)_hauteurImages, bindFlags: BindFlags.ShaderResource, resourceOptionFlags: ResourceOptionFlags.SharedNTHandle | ResourceOptionFlags.SharedKeyedMutex);
                    timestamp = _videoReader.ReadFrame(imageTexture.RawTexture);
                    if (timestamp < 0) return null;
                    // Now, if the next read sample time is more than NormalMaxGopDistance second apart from the desired one, it means that the gops are more than NormalMaxGopDistance seconds apart from each other... If so, we need to read all the samples until we reach the required sample time.
                    if (Math.Abs((imageVidéo.FrameTime - TimeSpan.FromSeconds(timestamp)).TotalSeconds) > NormalMaxGopDistance) scanAll = true;
                }
                else
                {
                    imageTexture = ImageTexturePool.PuiserImage(MfEav.MFInitialization.DirectXParameters, _videoReader.CurrentFormat == D3D11VideoReader.VideoFormat.NV12 ? Vortice.DXGI.Format.NV12 : Vortice.DXGI.Format.B8G8R8A8_UNorm, (uint)_largeurImages, (uint)_hauteurImages, bindFlags: BindFlags.ShaderResource, resourceOptionFlags: ResourceOptionFlags.SharedNTHandle | ResourceOptionFlags.SharedKeyedMutex);
                    timestamp = _videoReader.ReadFrame(imageTexture.RawTexture);
                    if (timestamp < 0) return null;
                }
                //General.WriteDebug($"Lecteur id: {Id}. Read ImageVidéo: {imageVidéo.FrameTime}. sample time: {timestamp}.");
                if (timestamp == 0.0) // End of stream or error
                {
                    // Pour les cas où le dernier frame a un timestamp légèrement plus petit que la dernière ImageVidéo.
                    // Aussi pour les cas où l'utilisateur aurait raccourci la vidéo originale.
                    imageTexture = _lastFrameRead;
                    return imageTexture;
                }
                if (_beforeLastFrameRead != null)
                {
                    _beforeLastFrameRead?.Dispose();
                }
                _beforeLastFrameRead = _lastFrameRead;
                _beforeLastFrameTimestamp = _lastFrameTimestamp;
                _lastFrameRead = imageTexture;
                _lastFrameTimestamp = timestamp;
            }
        }

        private ID3D11Texture2D CreateTexture()
        {
            var device = MfEav.MFInitialization.DirectXParameters.D3D11Device;
            var format = _transparentFrames ? Vortice.DXGI.Format.B8G8R8A8_UNorm : Vortice.DXGI.Format.NV12;

            var desc = new Vortice.Direct3D11.Texture2DDescription
            {
                Width = (uint)_largeurImages,
                Height = (uint)_hauteurImages,
                MipLevels = 1,
                ArraySize = 1,
                Format = format,
                SampleDescription = new Vortice.DXGI.SampleDescription(1, 0),
                Usage = ResourceUsage.Default,
                BindFlags = BindFlags.ShaderResource,
                CPUAccessFlags = CpuAccessFlags.None,
                MiscFlags = ResourceOptionFlags.None
            };

            return device.CreateTexture2D(desc);
        }
        void ReleaseFrames()
        {
            if (_beforeLastFrameRead != null)
            {
                ImageTexturePool.RetournerImage(_beforeLastFrameRead);
                _beforeLastFrameRead = null;
            }
            if (_lastFrameRead != null)
            {
                ImageTexturePool.RetournerImage(_lastFrameRead);
                _lastFrameRead.Dispose();
                _lastFrameRead = null;
            }
        }
        public void Dispose()
        {
            ReleaseFrames();
            if (_videoReader != null) _videoReader.Dispose();
            _videoReader = null;
            GC.SuppressFinalize(this);
        }
    }

}
